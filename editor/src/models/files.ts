import { ActionsType, Model } from '@topwrite/common';
import { socket } from '../lib/socket';
import { SearchResult } from 'repo';

interface FilesType {
    mode: 'search' | 'replace';
    query: string;
    replace: string;
    results: SearchResult[];
    status: 'loading' | 'error' | 'idle';
    active: string | null;
}

class Files extends Model<FilesType> {
    initialState: FilesType = {
        mode: 'search',
        query: '',
        replace: '',
        results: [],
        status: 'idle',
        active: null,
    };

    lock = false;

    subscription({ checkRefresh }: ActionsType<Files>) {
        socket.on('file.change', () => {
            if (!this.lock) {
                checkRefresh();
            } else {
                this.lock = false;
            }
        });
    }

    async *checkRefresh() {
        const { query } = yield *this.getState('files');

        if (query) {
            yield *this.search(query);
        }
    }

    *setMode(mode: 'search' | 'replace') {
        yield this.setState(state => {
            state.mode = mode;
        });
    }

    *setReplace(replace: string) {
        yield this.setState(state => {
            state.replace = replace;
        });
    }

    async *search(query: string) {
        query = query.trim();

        if (query) {
            yield this.setState(state => {
                state.query = query;
                state.status = 'loading';
                state.active = null;
            });

            const results = await socket.searchFile(query);

            yield this.setState(state => {
                state.results = results;
                state.status = 'idle';
            });
        } else {
            yield this.setState(state => {
                state.query = query;
                state.status = 'idle';
                state.results = [];
            });
        }
    }

    async *setActive(active: string) {
        yield this.setState(state => {
            state.active = active;
        });
    }

}

export const files = new Files();
