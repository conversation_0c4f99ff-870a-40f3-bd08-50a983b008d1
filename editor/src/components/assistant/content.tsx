import { Markdown, styled } from '@topwrite/common';
import { Fragment } from 'react';

export type ContentType = string | { type: 'image', image: string } | { type: 'chart', chart: string }

interface Props {
    value: ContentType | ContentType[];
}

export default function Content({ value }: Props) {
    const renderItem = (item: ContentType) => {
        if (typeof item === 'string') {
            if (item.trim()) {
                return <Markdown as={MarkdownContainer} aside={false}>{item}</Markdown>;
            }
        } else if (item.type === 'image') {
            return null;
        }
        return null;
    };

    if (Array.isArray(value)) {
        return <>
            {value.map((item, index) => <Fragment key={index}>
                {renderItem(item)}
            </Fragment>)}
        </>;
    } else {
        return renderItem(value);
    }
}

const MarkdownContainer = styled.div`
    overflow: hidden;

    code {
        white-space: pre-line;
    }

    &:not(:last-child) {
        margin-bottom: 0.5rem;
    }
`;
