import { request, styled, useAsync, useOptions } from '@topwrite/common';
import { memo } from 'react';
import Loader from '../loader';
import { PaneHeader } from '../pane';
import Action from './action';
import { Conversation, Provider, useContext } from './context';
import HistoryList from './history-list';
import InputBox from './input-box';
import MessageList from './message-list';

// 内部组件，使用 context
function AssistantContent() {
    const { windowState } = useContext();

    const getWindowTitle = () => {
        switch (windowState) {
            case 'history':
                return '历史记录';
            case 'settings':
                return '设置';
            default:
                return '聊天';
        }
    };

    const renderWindowContent = () => {
        switch (windowState) {
            case 'history':
                return <HistoryList />;
            case 'settings':
                return <div style={{ padding: '1rem' }}>设置功能开发中...</div>;
            default:
                return <>
                    <MessageList />
                    <InputBox />
                </>;
        }
    };

    return <Container>
        <PaneHeader action={<Action />}>
            {getWindowTitle()}
        </PaneHeader>
        <Body>
            {renderWindowContent()}
        </Body>
    </Container>;
}

const Assistant = memo(() => {
    const { assistant } = useOptions();

    const { result: { conversation } = {} } = useAsync<{ conversation: Conversation }>(async () => {
        const { data } = await request.get(`${assistant}`);
        return data;
    }, []);

    if (conversation === undefined) {
        return <Loader />;
    }

    return <Provider conversation={conversation}>
        <AssistantContent />
    </Provider>;
}, () => true);

export default Assistant;

const Container = styled.div`
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
`;

const Body = styled.div`
    flex: auto;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
`;
