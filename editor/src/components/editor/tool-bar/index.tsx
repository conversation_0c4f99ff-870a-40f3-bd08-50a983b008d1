import { styled, useActions, useSelector } from '@topwrite/common';
import { useEffect, useRef, useState } from 'react';
import { ButtonGroup, ButtonToolbar } from 'react-bootstrap';
import { createPortal } from 'react-dom';
import { usePlate } from '../use-plate';

export default function ToolBar() {
    const { tools: [leftGroups, rightGroups] } = usePlate();
    const { toolbar } = useSelector('workspace');

    if (!toolbar) {
        return null;
    }

    return createPortal(<ButtonToolbar className='justify-content-between flex-nowrap'>
        <ButtonToolbar data-tour={'editor-tools'}>
            {leftGroups.map((group, i) => {
                return <ButtonGroup key={i}>
                    {group.map((Component, j) => <Component key={j} />)}
                </ButtonGroup>;
            })}
        </ButtonToolbar>
        <ButtonToolbar>
            {rightGroups.map((group, i) => {
                return <ButtonGroup key={i}>
                    {group.map((Component, j) => <Component key={j} />)}
                </ButtonGroup>;
            })}
        </ButtonToolbar>
    </ButtonToolbar>, toolbar);
}

export const ToolBarContainer = function() {
    const [height, setHeight] = useState(43);
    const divRef = useRef<HTMLDivElement>(null);
    const { setToolbar } = useActions('workspace');

    useEffect(() => {
        const element = divRef.current;
        setToolbar(element);
        if (!element) return;
        const updateHeight = () => {
            // 获取所有直接子元素的高度
            const children = Array.from(element.children) as HTMLElement[];
            const childrenHeight = children.reduce((total, child) => {
                return total + child.offsetHeight;
            }, 0);

            // 获取当前元素的 padding 和 border 值
            const computedStyle = window.getComputedStyle(element);
            const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
            const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
            const borderTop = parseFloat(computedStyle.borderTopWidth) || 0;
            const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0;
            const totalExtra = paddingTop + paddingBottom + borderTop + borderBottom;

            setHeight(Math.max(childrenHeight + totalExtra, 43));
        };

        const resizeObserver = new ResizeObserver(() => {
            updateHeight();
        });

        // 监听所有子元素
        const observeChildren = () => {
            const children = Array.from(element.children) as HTMLElement[];
            children.forEach(child => {
                resizeObserver.observe(child);
            });
        };

        // 初始化监听
        observeChildren();
        updateHeight();

        // 监听子元素的添加和删除
        const mutationObserver = new MutationObserver(() => {
            resizeObserver.disconnect();
            observeChildren();
            updateHeight();
        });

        mutationObserver.observe(element, {
            childList: true,
            subtree: false
        });

        return () => {
            resizeObserver.disconnect();
            mutationObserver.disconnect();
        };
    }, []);

    return <ToolBarContainerInner ref={divRef} style={{ minHeight: height }} />;
};

const ToolBarContainerInner = styled.div`
    position: relative;
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 4px 5px;
    background: var(--ttw-background);

    .btn {
        line-height: 1;
        padding: 0.5rem 0.75rem;
        height: 34px;
        width: 40px;
        color: var(--ttw-color);

        svg {
            font-size: 16px;
        }
    }

    .btn-toolbar {
        gap: 0.25rem;

        & > .btn-group:empty {
            margin: 0 -0.125rem;
        }
    }
`;
